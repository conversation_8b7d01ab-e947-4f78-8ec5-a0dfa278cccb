# Go 通用游戏服务器框架设计文档 (Project: Genesis)

## 1. 概述 (Overview)

### 1.1. 项目目标 (Goals)

Genesis 框架旨在成为一个通用的、适配性高的、易于上手的 Go 语言游戏服务器框架。其核心目标是：

-   **通用性 (Genericity):** 适用于多种游戏类型，如 MMORPG、ARPG、卡牌、SLG 等。
-   **适配性 (Adaptability):** 核心功能模块化、可插拔，方便开发者替换或扩展特定实现。
-   **易上手 (Ease of Use):** 提供简洁的 API 和合理的默认配置，降低新项目启动门槛。
-   **高性能 (High-Performance):** 在关键路径上（如网络、序列化、日志）采用高性能库和模式。
-   **可靠性 (Reliability):** 内置平滑重启、结构化日志、性能分析等生产环境所需的特性。

### 1.2. 设计哲学 (Design Philosophy)

-   **模块化与可插拔:** 框架由一系列正交的 **Component** 构成。每个组件关注独立的领域（如网络、会话、场景管理），通过清晰的接口进行交互。
-   **面向接口编程:** 框架的核心能力均通过接口（Interface）定义，开发者可以轻松地替换任何默认实现，例如将内存缓存替换为 Redis，或将 TCP 替换为 KCP。
-   **约定优于配置:** 框架提供一套合理的默认行为和配置，开发者只需编写游戏逻辑即可快速运行。同时，所有配置项均可被轻松覆盖以满足个性化需求。
-   **拥抱 Go 原生并发:** 充分利用 Goroutine 和 Channel 构建核心并发模型，优先通过消息传递来共享内存，以降低锁竞争和死锁风险，使并发逻辑更清晰。

## 2. 核心架构 (Core Architecture)

### 2.1. 分层架构

框架采用经典的分层架构，各层职责分明，自下而上支撑整个游戏世界。

```mermaid
graph TD
    subgraph Client [客户端]
        A[Game Client]
    end

    subgraph Server [Genesis 框架]
        B(网络层 Network Layer) --> C(会话层 Session Layer)
        C --> D(应用/逻辑层 Application/Logic Layer)
        D --> E(数据/服务层 Data/Service Layer)
    end

    subgraph Dependencies [底层依赖]
        F[数据库 Databases]
        G[缓存 Caches]
        H[其他微服务 Other Services]
    end

    A -- TCP/WebSocket/KCP --> B
    D -- 访问 --> E
    E -- 连接 --> F
    E -- 连接 --> G
    E -- RPC --> H

    style Client fill:#cde4ff
    style Dependencies fill:#f9f9f9
```

-   **网络层 (Network Layer):** 负责处理底层的网络协议（TCP, WebSocket, KCP 等），接收原始字节流，并通过编解码器（Codec）将其转换为结构化的消息对象。
-   **会话层 (Session Layer):** 代表一个与客户端建立的连接。每个会话（Session）在独立的 Goroutine 中运行，负责管理连接生命周期、心跳、消息的收发，并将收到的消息路由到应用层。
-   **应用/逻辑层 (Application/Logic Layer):** 这是游戏核心逻辑的所在地。它由多个 **Component** 组成，如场景组件、聊天组件、工会组件等。组件之间通过内部 RPC（`chanrpc`）通信。
-   **数据/服务层 (Data/Service Layer):** 提供数据持久化和缓存的抽象接口，供应用层调用。

### 2.2. 核心并发模型

本框架采用 **Goroutine-Per-Session** 结合 **Channel-Based-RPC** 的并发模型。

-   **Goroutine-Per-Session:** 每个客户端连接成功后，框架会为其创建一个专属的 `Session` 对象，并启动一个独立的 Goroutine 来管理它。此 Goroutine 负责从此连接读取数据，并将处理结果写回。这确保了对单个连接的读写操作是隔离的。
-   **Channel-Based-RPC:** 游戏逻辑被划分到不同的 `Component` 中，每个 `Component` 在自己的 Goroutine 中运行。`Session` 或 `Component` 之间不直接调用方法，而是通过一个基于 Channel 的 RPC 机制（`chanrpc`）发送消息。这保证了每个 `Component` 内部的状态是串行处理的，天然避免了数据竞争，无需在业务逻辑中显式使用互斥锁。

## 3. 模块定义 (Module Definitions)

项目结构遵循 Go 社区标准，所有核心模块位于 `pkg/` 目录下。

### `pkg/server` - 服务器核心
**目的:** 管理服务器的完整生命周期，包括启动、关闭和平滑重启。
**核心接口/结构:**
```go
// server.go
type Server struct {
    // ...
}

func NewServer() *Server
func (s *Server) AddAcceptor(addr string, protocol string) // 添加监听器
func (s *Server) AddComponent(c component.Component)      // 注册组件
func (s *Server) Start() error                            // 启动服务器
func (s *Server) Shutdown(ctx context.Context)            // 平滑关闭
```
**实现要点:** 内部管理一个 `Acceptor` 列表和一个 `Component` 列表。`Start` 方法会启动所有组件和监听器。`Shutdown` 方法会利用 `context` 和 `sync.WaitGroup` 优雅地关闭所有服务，确保正在处理的请求可以完成。

### `pkg/network` - 网络层
**目的:** 抽象底层网络协议，提供统一的连接和消息处理接口。
**核心接口/结构:**
```go
// interfaces.go
type Acceptor interface {
    Listen() error
    Close()
}

type Connection interface {
    ReadMsg() ([]byte, error)
    WriteMsg(data []byte) error
    RemoteAddr() net.Addr
    Close()
}

type Codec interface {
    Encode(v interface{}) ([]byte, error)
    Decode(data []byte) (interface{}, error)
}
```
**推荐实现:**
-   **TCP:** 使用标准库 `net`。
-   **WebSocket:** 使用 `github.com/coder/websocket`，因其高性能和现代化的 API。
-   **KCP:** 使用 `github.com/xtaci/kcp-go`，作为可靠 UDP 的生产级方案。
-   **HTTP:** 使用 `github.com/go-chi/chi` 作为 HTTP 路由，用于 GM 命令、监控接口等。
-   **Codec:** 默认提供 JSON 和 Protobuf 的实现。

### `pkg/session` - 会话层
**目的:** 代表一个已连接的客户端，管理其状态并作为网络层和应用层的桥梁。
**核心接口/结构:**
```go
// session.go
type Session struct {
    SID      int64 // Unique session ID
    UID      int64 // User ID, 0 if not authenticated
    conn     network.Connection
    sendChan chan []byte // Buffered channel for outgoing messages
    // ...
}

func NewSession(conn network.Connection) *Session
func (s *Session) Run() // Starts the read/write loops
func (s *Session) Close()
func (s *Session) Bind(uid int64)
```

### `pkg/component` - 组件
**目的:** 游戏逻辑的组织单元。
**核心接口/结构:**
```go
// component.go
type Component interface {
    Name() string
    Init(s *server.Server) error
    AfterInit()
    BeforeShutdown()
    Shutdown()
}
```
**实现要点:** 每个组件内部可以包含一个 `chanrpc` 服务器，用于接收来自 `Session` 或其他组件的调用请求。

### `pkg/gacha` - 概率与抽奖
**目的:** 提供通用的抽奖、开宝箱等概率玩法算法。
**核心接口/结构:**
```go
// alias.go - 别名算法
type AliasTable struct { /* ... */ }
func NewAliasTable(weights map[uint32]float64) (*AliasTable, error)
func (t *AliasTable) Sample() uint32 // O(1) time complexity

// pity.go - 保底机制
type PityPool struct {
    pool         *AliasTable // Base gacha pool
    pityCounter  int         // Pity counter
    pityTrigger  int         // Pity threshold
    guaranteedID uint32      // Guaranteed item on pity
}
func (p *PityPool) Draw() uint32
```

### `pkg/storage` - 数据存储
**目的:** 提供数据库访问的抽象接口。
**核心接口/结构:**
```go
// player_storage.go
type PlayerStorage interface {
    Load(uid int64) (*PlayerData, error)
    Save(player *PlayerData) error
    Create(player *PlayerData) error
}
```
**推荐实现:** 推荐使用 `github.com/jmoiron/sqlx`，它在原生 `database/sql` 之上提供了便利性，且无性能损失。避免在核心框架中强制绑定 GORM 等重型 ORM。

### `pkg/cache` - 缓存
**目的:** 提供统一的内存缓存和分布式缓存接口。
**核心接口/结构:**
```go
// cache.go
type Cache interface {
    Get(key string) (interface{}, bool)
    Set(key string, value interface{}, duration time.Duration)
    Delete(key string)
}
```
**推荐实现:**
-   **内存缓存:** 包装 `github.com/patrickmn/go-cache`。
-   **分布式缓存:** 包装 `github.com/go-redis/redis`。

### `pkg/log` & `pkg/config` - 日志与配置
-   **日志:**
    -   **推荐库:** `go.uber.org/zap`，因其卓越的性能。
    -   **实现:** 在 `pkg/log` 中初始化一个全局的、可配置的 `zap.Logger`，提供 `log.Info`, `log.Warn`, `log.Error` 等封装方法。
-   **配置:**
    -   **推荐库:** `github.com/spf13/viper`。
    -   **实现:** 在 `pkg/config` 中初始化 Viper，支持从 `config.yaml` 文件、环境变量中读取配置。

## 4. 同步机制支持

-   **状态同步 (State Synchronization):** 框架的默认模型。客户端发送操作请求，服务器更新权威状态，并将状态变化广播给相关客户端。此模型天然适用于 RPG、SLG 等游戏。
-   **帧同步 (Frame Synchronization):** 框架通过提供特定的“房间（Room）”组件来支持。房间组件负责收集在同一逻辑帧内所有客户端的操作，并将操作集广播给房间内所有客户端。框架本身不保证客户端的确定性表现，但为帧同步提供了必要的通信基础。

## 5. 设计示例: 玩家登录流程

下面是一个完整的玩家登录流程，以展示各模块如何协同工作。

1.  **客户端连接:** `network.TCPAcceptor` 监听到一个新的 TCP 连接。
2.  **会话创建:** 服务器为该连接创建一个 `Session` 实例，并启动其 `Run()` 方法所在的 Goroutine。
3.  **发送登录请求:** 客户端使用 Protobuf 序列化一个包含用户 Token 的 `LoginRequest` 消息，并通过连接发送。
4.  **消息解码与路由:**
    -   `Session` 的 Goroutine 从 `Connection` 中读取到字节流。
    -   `network.ProtobufCodec` 将字节流解码为 `LoginRequest` 对象。
    -   `Session` 根据消息 ID 或路由信息，确定该消息应由 `AuthComponent` 处理。
    -   `Session` 将请求通过 `chanrpc` 发送给 `AuthComponent`。
5.  **业务逻辑处理:**
    -   `AuthComponent` 的 Goroutine 从其 channel 中收到请求。
    -   它调用 `storage.PlayerStorage` 接口，访问数据库验证 Token 并加载玩家数据。
    -   数据加载成功后，调用 `session.Bind(uid)` 将会话与玩家 UID 绑定。
6.  **响应客户端:**
    -   `AuthComponent` 创建一个 `LoginResponse` 消息（包含玩家数据）。
    -   `LoginResponse` 通过 `chanrpc` 被发回给 `Session`。
    -   `Session` 的 Goroutine 接收到响应对象，通过 `ProtobufCodec` 编码成字节流，再通过 `Connection` 写回给客户端。
7.  **登录完成:** 客户端收到登录成功的响应，进入游戏主场景。

## 6. 工程化实践

### 项目结构

```
/
├── cmd/server/main.go   # 服务器主程序入口
├── configs/config.yaml  # 默认配置文件
├── internal/            # 项目内部私有包
├── pkg/                 # 可被外部项目引用的核心库
│   ├── server/
│   ├── network/
│   └── ...
├── scripts/             # 构建、部署脚本
├── api/proto/           # Protobuf定义文件
├── go.mod
├── Makefile
└── Dockerfile
```

### Makefile

提供常用命令。

```makefile
.PHONY: all build run test clean docker

build:
    go build -o app ./cmd/server/main.go

run:
    go run ./cmd/server/main.go

test:
    go test ./...

docker:
    docker build -t mygame:latest .
```

### 性能分析

在 `main.go` 中默认引入 `net/http/pprof`，并通过独立的端口暴露，方便在运行时进行性能分析。

### Docker 容器化

提供一个使用多阶段构建（Multi-stage builds）的 `Dockerfile`，以生成最小化的生产环境镜像。

```dockerfile
# ---- Build Stage ----
FROM golang:1.22-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o app ./cmd/server/main.go

# ---- Final Stage ----
FROM alpine:latest
WORKDIR /root/
COPY --from=builder /app/app .
COPY --from=builder /app/configs/ ./configs/
# Expose game server port and pprof port
EXPOSE 8888 6060
CMD ["./app"]
```
